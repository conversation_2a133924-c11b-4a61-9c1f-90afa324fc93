# Gemini CLI OpenAI Worker Environment Variables - STAGING Environment

# Required: OA<PERSON>2 credentials <PERSON><PERSON><PERSON> from Gemini CLI authentication
# Get this by running `gemini auth` and copying the contents of ~/.gemini/oauth_creds.json
# 注意：测试环境应该使用不同的Google账户或项目
GCP_SERVICE_ACCOUNT={
  "access_token": "************************************************************************************************************************************************************************************************************************************",
  "refresh_token": "1//03DvEvXKM0l0jCgYIARAAGAMSNwF-L9Ir4avmpFZRscGakyk4tFhYXEWfQ-Flg6XbPUqXJftEE59ZNXbRSfkIIMPxqjjZHBVklYM",
  "scope": "openid https://www.googleapis.com/auth/cloud-platform https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile",
  "token_type": "Bearer",
  "id_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImRkNTMwMTIwNGZjMWQ2YTBkNjhjNzgzYTM1Y2M5YzEwYjI1ZTFmNGEiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bwlYC6QjrEG-e_do-ifiix1W0r20mvGzJzCKZW6GOYoRJEtMCAybi5OocZtMZrtBEjB_-swaTRZRx2uHx7TpOVVSt-4HoXAvNt-VLsCLkax7P7gTPHEaZDAJ6Std7xxcAV7KClD-RkyWNQ-bglDK3YzmeKREY4Od1uOY7x7nErSqyQRsLfFXUnZeCElxnKO91Y_fKJ5KXJKykLfhcgxVDw_R_2u2gww6zSi41GvDHhvLkVcA-_4TbEcLlh2IHJIBkRl-VGtQadGvHf-J6WQKNRpmcLVHdFF-0K3TGX4x2G-gkoxwOpdaN8s6HX05dmPHpoIxzJw0-aUFu_TVgBtnqg",
  "expiry_date": *************
}

# Optional: Google Cloud Project ID (建议为测试环境使用独立项目)
GEMINI_PROJECT_ID=august-apricot-391904

# Optional: API key for authentication (测试环境使用不同的API密钥)
# When set, clients must include "Authorization: Bearer <your-api-key>" header
OPENAI_API_KEY=sk-zxcvb1234567890qwertasdfg

# Optional: Enable fake thinking output for thinking models
# 测试环境可以启用假思考模式进行测试
ENABLE_FAKE_THINKING=false

# Optional: Enable real Gemini thinking output
# 测试环境建议启用真实思考模式
ENABLE_REAL_THINKING=true

# Optional: Stream thinking as content with <thinking> tags (DeepSeek R1 style)
STREAM_THINKING_AS_CONTENT=true

# Optional: Auto switch from Pro to flash when you are getting rate-limited
ENABLE_AUTO_MODEL_SWITCHING=true

# Optional: Gemini Moderation Settings for staging
# 测试环境可以使用较宽松的审核设置
GEMINI_MODERATION_HARASSMENT_THRESHOLD=BLOCK_FEW
GEMINI_MODERATION_HATE_SPEECH_THRESHOLD=BLOCK_FEW
GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD=BLOCK_SOME
GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD=BLOCK_SOME
