# Debug authentication issue
$headers = @{
    'Content-Type' = 'application/json'
}

$baseUrl = 'https://gemini-cli-worker-testing.13467879663.workers.dev'

Write-Host "=== Debugging Authentication Issue ===" -ForegroundColor Green

# Test the root endpoint to see current auth status
Write-Host "1. Checking root endpoint for auth status..." -ForegroundColor Yellow
try {
    $rootResponse = Invoke-WebRequest -Uri "$baseUrl/" -Method GET
    $rootData = $rootResponse.Content | ConvertFrom-Json
    Write-Host "Auth required: $($rootData.authentication.required)" -ForegroundColor Cyan
    Write-Host "Auth type: $($rootData.authentication.type)" -ForegroundColor Cyan
} catch {
    Write-Host "Error getting root: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test chat completions with detailed error
Write-Host "2. Testing chat completions with detailed error..." -ForegroundColor Yellow
$body = @{
    model = "gemini-2.5-flash"
    messages = @(
        @{
            role = "user"
            content = "Hello"
        }
    )
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/v1/chat/completions" -Method POST -Headers $headers -Body $body
    Write-Host "Success: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error Body: $errorBody" -ForegroundColor Yellow
            
            if ($errorBody) {
                $errorJson = $errorBody | ConvertFrom-Json
                Write-Host "Error Type: $($errorJson.error.type)" -ForegroundColor Cyan
                Write-Host "Error Code: $($errorJson.error.code)" -ForegroundColor Cyan
                Write-Host "Error Message: $($errorJson.error.message)" -ForegroundColor Cyan
            }
        } catch {
            Write-Host "Could not parse error response" -ForegroundColor Red
        }
    }
}

Write-Host ""

# Test debug endpoints
Write-Host "3. Testing debug endpoints..." -ForegroundColor Yellow
try {
    $debugResponse = Invoke-WebRequest -Uri "$baseUrl/v1/debug/cache" -Method GET
    Write-Host "Debug cache: $($debugResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Debug cache error: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    $tokenTestResponse = Invoke-WebRequest -Uri "$baseUrl/v1/token-test" -Method POST -Headers $headers
    Write-Host "Token test: $($tokenTestResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Token test error: $($_.Exception.Message)" -ForegroundColor Red
}
