# 从.staging.vars文件中读取GEMINI_PROJECT_ID值
$varFile = Get-Content -Path ".staging.vars" -Raw
$pattern = 'GEMINI_PROJECT_ID=([^\r\n]+)'
$matches = [regex]::Match($varFile, $pattern)

if ($matches.Success -and $matches.Groups.Count -gt 1) {
    $projectId = $matches.Groups[1].Value.Trim()
    
    # 显示找到的值
    Write-Host "找到GEMINI_PROJECT_ID值:" -ForegroundColor Green
    Write-Host $projectId -ForegroundColor Cyan

    # 将值保存到临时文件
    $tempFile = [System.IO.Path]::GetTempFileName()
    $projectId | Out-File -FilePath $tempFile -Encoding utf8

    # 使用wrangler更新密钥
    Write-Host "正在更新Wrangler密钥..." -ForegroundColor Yellow
    Get-Content -Path $tempFile | wrangler secret put GEMINI_PROJECT_ID --env staging

    # 清理临时文件
    Remove-Item -Path $tempFile
} else {
    Write-Host "在.staging.vars文件中未找到GEMINI_PROJECT_ID值" -ForegroundColor Red
    exit 1
} 