# Test the API without authentication
$headers = @{
    'Content-Type' = 'application/json'
}

$baseUrl = 'https://gemini-cli-worker-testing.13467879663.workers.dev'

Write-Host "=== Testing API without Authentication ===" -ForegroundColor Green
Write-Host "Base URL: $baseUrl" -ForegroundColor Cyan
Write-Host ""

# Test 1: Valid request (should work now)
Write-Host "Test 1: Valid chat completion request" -ForegroundColor Yellow
$validBody = @{
    model = "gemini-2.5-flash"
    messages = @(
        @{
            role = "user"
            content = "Hello! Please respond with just 'API is working'"
        }
    )
    stream = $false
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/v1/chat/completions" -Method POST -Headers $headers -Body $validBody -TimeoutSec 30
    Write-Host "✅ Status: $($response.StatusCode)" -ForegroundColor Green
    $responseData = $response.Content | ConvertFrom-Json
    Write-Host "✅ Response received: $($responseData.choices[0].message.content)" -ForegroundColor Green
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error Response: $errorBody" -ForegroundColor Red
    }
}

Write-Host ""

# Test 2: JSON parsing error (should return 400 with enhanced error)
Write-Host "Test 2: JSON parsing error test" -ForegroundColor Yellow
$malformedBody = '{"model": "gem\ini-2.5-flash", "messages": [{"role": "user", "content": "Hello"}]}'

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/v1/chat/completions" -Method POST -Headers $headers -Body $malformedBody -TimeoutSec 30
    Write-Host "❌ Unexpected success: $($response.StatusCode)" -ForegroundColor Red
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "✅ Got expected error. Status: $statusCode" -ForegroundColor Green
    
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error Response: $errorBody" -ForegroundColor Cyan
        
        if ($statusCode -eq 400) {
            Write-Host "🎉 JSON parsing fix is working! (400 instead of 500)" -ForegroundColor Green
        }
    }
}

Write-Host ""

# Test 3: Models endpoint
Write-Host "Test 3: Models list" -ForegroundColor Yellow
try {
    $modelsResponse = Invoke-WebRequest -Uri "$baseUrl/v1/models" -Method GET -TimeoutSec 15
    Write-Host "✅ Models endpoint: $($modelsResponse.StatusCode)" -ForegroundColor Green
    $modelsData = $modelsResponse.Content | ConvertFrom-Json
    Write-Host "Available models: $($modelsData.data.Count)" -ForegroundColor Green
    $modelsData.data | ForEach-Object { Write-Host "  - $($_.id)" -ForegroundColor Cyan }
} catch {
    Write-Host "❌ Models endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Updated API Information ===" -ForegroundColor Green
Write-Host "🚀 Server URL: $baseUrl" -ForegroundColor White
Write-Host "🔓 Authentication: Not required (public API)" -ForegroundColor White
Write-Host "✅ JSON parsing errors: Fixed (returns 400 instead of 500)" -ForegroundColor White
Write-Host ""
Write-Host "📋 Available endpoints:" -ForegroundColor White
Write-Host "  GET  /health" -ForegroundColor Cyan
Write-Host "  GET  /" -ForegroundColor Cyan
Write-Host "  GET  /v1/models" -ForegroundColor Cyan
Write-Host "  POST /v1/chat/completions" -ForegroundColor Cyan
Write-Host "  GET  /v1/debug/cache" -ForegroundColor Cyan
Write-Host "  POST /v1/token-test" -ForegroundColor Cyan
Write-Host "  POST /v1/test" -ForegroundColor Cyan
