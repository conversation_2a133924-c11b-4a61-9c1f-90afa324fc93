{"name": "Gemini CLI OpenAI Worker", "description": "OpenAI-compatible API for Google Gemini models via OAuth", "version": "1.0.0", "authentication": {"required": false, "type": "None"}, "endpoints": {"chat_completions": "/v1/chat/completions", "models": "/v1/models", "debug": {"cache": "/v1/debug/cache", "token_test": "/v1/token-test", "full_test": "/v1/test"}}, "documentation": "https://github.com/gewoonjaap/gemini-cli-openai"}