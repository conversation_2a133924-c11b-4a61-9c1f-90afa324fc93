# PowerShell script for multi-environment deployment automation
# Usage: .\deploy-multi-env.ps1 [environment] [action]
# Example: .\deploy-multi-env.ps1 staging deploy

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "testing", "staging", "pre-production", "production", "all")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [ValidateSet("setup", "deploy", "test", "status")]
    [string]$Action
)

# Color output functions
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }
function Write-Info { Write-ColorOutput Cyan $args }

# Check for required tools
function Check-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    if (!(Test-Path "C:\Program Files\nodejs\npm.cmd")) {
        Write-Error "Error: npm.cmd not found at 'C:\Program Files\nodejs\npm.cmd'"
        exit 1
    }
    
    Write-Success "✓ All prerequisites are installed"
}

# Create KV namespace
function Setup-KVNamespace($env) {
    Write-Info "Creating KV namespace for $env environment..."
    
    if ($env -eq "dev") {
        Write-Warning "Development environment uses existing KV namespace"
        return
    }
    
    $result = wrangler kv namespace create "GEMINI_CLI_KV" --env $env 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "✓ KV namespace for $env created successfully"
        Write-Warning "Please update wrangler.toml with the returned namespace ID"
        Write-Output $result
    } else {
        Write-Error ("X Failed to create KV namespace for " + $env + ": " + $result)
    }
}

# Setup environment secrets
function Setup-Secrets($env) {
    Write-Info "Setting up secrets for $env environment..."
    
    $envFile = switch ($env) {
        "dev" { ".dev.vars" }
        "testing" { ".testing.vars" }
        "staging" { ".staging.vars" }
        "pre-production" { ".pre-production.vars" }
        "production" { ".production.vars" }
    }
    
    if (!(Test-Path $envFile)) {
        Write-Error "Error: Environment file $envFile not found"
        return
    }
    
    Write-Warning "Please set the following secrets manually:"
    if ($env -eq "dev") {
        Write-Output "wrangler secret put GCP_SERVICE_ACCOUNT"
        Write-Output "wrangler secret put OPENAI_API_KEY"
    } else {
        Write-Output "wrangler secret put GCP_SERVICE_ACCOUNT --env $env"
        Write-Output "wrangler secret put OPENAI_API_KEY --env $env"
    }
}

# Deploy environment
function Deploy-Environment($env) {
    Write-Info "Deploying to $env environment..."
    
    $npmCmd = "& 'C:\Program Files\nodejs\npm.cmd'"

    $deployCmd = switch ($env) {
        "dev" { "$npmCmd run deploy:dev" }
        "testing" { "$npmCmd run deploy:testing" }
        "staging" { "$npmCmd run deploy:staging" }
        "pre-production" { "$npmCmd run deploy:pre-prod" }
        "production" { "$npmCmd run deploy:prod" }
    }
    
    Write-Info "Executing command: $deployCmd"
    Invoke-Expression $deployCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "✓ Deployment to $env environment successful"
    } else {
        Write-Error ("X Deployment to " + $env + " environment failed")
    }
}

# Test environment
function Test-Environment($env) {
    Write-Info "Testing $env environment..."
    
    $workerName = switch ($env) {
        "dev" { "gemini-cli-worker-dev" }
        "testing" { "gemini-cli-worker-testing" }
        "staging" { "gemini-cli-worker-staging" }
        "pre-production" { "gemini-cli-worker-pre-prod" }
        "production" { "gemini-cli-worker-prod" }
    }
    
    # Get worker URL
    $url = "https://$workerName.your-subdomain.workers.dev"
    Write-Info "Testing URL: $url"
    
    try {
        $response = Invoke-RestMethod -Uri "$url/health" -Method Get -TimeoutSec 10
        Write-Success "✓ Health check passed: $($response.status)"
        
        $modelsResponse = Invoke-RestMethod -Uri "$url/v1/models" -Method Get -TimeoutSec 10
        Write-Success "✓ Successfully fetched models list, total $($modelsResponse.data.Count) models"
    } catch {
        Write-Error ("X Environment test failed: " + $_.Exception.Message)
    }
}

# Get environment status
function Get-EnvironmentStatus($env) {
    Write-Info "Getting status for $env environment..."
    
    $npxCmd = "& 'C:\Program Files\nodejs\npx.cmd'"

    if ($env -eq "dev") {
        Invoke-Expression "$npxCmd wrangler deployments list"
        Invoke-Expression "$npxCmd wrangler secret list"
    } else {
        Invoke-Expression "$npxCmd wrangler deployments list --env $env"
        Invoke-Expression "$npxCmd wrangler secret list --env $env"
    }
}

# Main logic
Check-Prerequisites

switch ($Environment) {
    "all" {
        $environments = @("dev", "testing", "staging", "pre-production", "production")
        if ($Action -eq "setup") {
            foreach ($env in $environments) {
                if ($env -ne "dev") { Setup-KVNamespace $env }
                Setup-Secrets $env
            }
        } elseif ($Action -eq "deploy") {
            foreach ($env in $environments) {
                Deploy-Environment $env
            }
        } elseif ($Action -eq "test") {
            foreach ($env in $environments) {
                Test-Environment $env
            }
        } else {
            foreach ($env in $environments) {
                Get-EnvironmentStatus $env
            }
        }
    }
    default {
        switch ($Action) {
            "setup" {
                Setup-KVNamespace $Environment
                Setup-Secrets $Environment
            }
            "deploy" {
                Deploy-Environment $Environment
            }
            "test" {
                Test-Environment $Environment
            }
            "status" {
                Get-EnvironmentStatus $Environment
            }
        }
    }
}

Write-Success "Operation completed!"
