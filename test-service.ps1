param(
    [string]$Environment = "testing",
    [string]$ApiKey = "sk-zxcvb1234567890qwertasdfg"
)

# 根据环境设置基本URL
$baseUrl = "https://gemini-cli-worker-$Environment.13467879663.workers.dev"

# 定义headers
$headers = @{
    'Content-Type' = 'application/json'
    'Authorization' = "Bearer $ApiKey"
}

# 测试根端点
Write-Host "测试根端点..." -ForegroundColor Yellow
$rootResponse = curl -s $baseUrl
$rootResponseFile = "root-response.json"
$rootResponse > $rootResponseFile
Write-Host "根端点响应已保存至 $rootResponseFile" -ForegroundColor Green
Write-Host ""

# 测试模型列表
Write-Host "测试模型列表..." -ForegroundColor Yellow
$modelsResponse = curl -s "$baseUrl/v1/models" -H "Authorization: Bearer $ApiKey"
$modelsResponseFile = "models-response.json"
$modelsResponse > $modelsResponseFile
Write-Host "模型列表响应已保存至 $modelsResponseFile" -ForegroundColor Green
Write-Host ""

# 测试聊天完成
Write-Host "测试聊天完成..." -ForegroundColor Yellow
$chatData = @{
    model = "gemini-2.5-flash"
    stream = $false
    messages = @(
        @{
            role = "user"
            content = "Hello, please reply with: 'Testing environment is working properly!'"
        }
    )
} | ConvertTo-Json

$chatDataFile = "chat-request.json"
$chatData > $chatDataFile

$chatResponse = curl -s "$baseUrl/v1/chat/completions" -H "Content-Type: application/json" -H "Authorization: Bearer $ApiKey" -d $chatData
$chatResponseFile = "chat-response.json"
$chatResponse > $chatResponseFile
Write-Host "聊天完成响应已保存至 $chatResponseFile" -ForegroundColor Green
Write-Host ""

Write-Host "所有测试已完成!" -ForegroundColor Cyan
Write-Host "请检查生成的JSON文件以查看详细响应" -ForegroundColor Cyan 