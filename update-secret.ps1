# 从.staging.vars文件中读取GCP_SERVICE_ACCOUNT值
$varFile = Get-Content -Path ".staging.vars" -Raw
$pattern = '(?s)GCP_SERVICE_ACCOUNT=(\{.*?\})(?:\r?\n)'
$matches = [regex]::Match($varFile, $pattern)

if ($matches.Success -and $matches.Groups.Count -gt 1) {
    $serviceAccount = $matches.Groups[1].Value
    
    # 显示找到的值
    Write-Host "找到GCP_SERVICE_ACCOUNT值:" -ForegroundColor Green
    Write-Host $serviceAccount -ForegroundColor Cyan

    # 将值保存到临时文件
    $tempFile = [System.IO.Path]::GetTempFileName()
    $serviceAccount | Out-File -FilePath $tempFile -Encoding utf8

    # 使用wrangler更新密钥
    Write-Host "正在更新Wrangler密钥..." -ForegroundColor Yellow
    Get-Content -Path $tempFile | wrangler secret put GCP_SERVICE_ACCOUNT --env staging

    # 清理临时文件
    Remove-Item -Path $tempFile
} else {
    Write-Host "在.staging.vars文件中未找到GCP_SERVICE_ACCOUNT值" -ForegroundColor Red
    exit 1
} 