#!/bin/bash

# Bash 脚本：多环境部署自动化
# 使用方法: ./deploy-multi-env.sh [environment] [action]
# 示例: ./deploy-multi-env.sh staging deploy

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 输出函数
print_success() { echo -e "${GREEN}$1${NC}"; }
print_warning() { echo -e "${YELLOW}$1${NC}"; }
print_error() { echo -e "${RED}$1${NC}"; }
print_info() { echo -e "${CYAN}$1${NC}"; }

# 显示使用方法
show_usage() {
    echo "使用方法: $0 [environment] [action]"
    echo ""
    echo "环境选项:"
    echo "  dev        - 开发环境"
    echo "  staging    - 测试环境"
    echo "  production - 生产环境"
    echo "  all        - 所有环境"
    echo ""
    echo "操作选项:"
    echo "  setup      - 设置环境（创建KV命名空间和密钥）"
    echo "  deploy     - 部署到环境"
    echo "  test       - 测试环境"
    echo "  status     - 查看环境状态"
    echo ""
    echo "示例:"
    echo "  $0 staging deploy    # 部署到测试环境"
    echo "  $0 all setup        # 设置所有环境"
    echo "  $0 production test   # 测试生产环境"
}

# 检查参数
if [ $# -ne 2 ]; then
    show_usage
    exit 1
fi

ENVIRONMENT=$1
ACTION=$2

# 验证环境参数
case $ENVIRONMENT in
    dev|staging|production|all)
        ;;
    *)
        print_error "错误: 无效的环境参数 '$ENVIRONMENT'"
        show_usage
        exit 1
        ;;
esac

# 验证操作参数
case $ACTION in
    setup|deploy|test|status)
        ;;
    *)
        print_error "错误: 无效的操作参数 '$ACTION'"
        show_usage
        exit 1
        ;;
esac

# 检查必要工具
check_prerequisites() {
    print_info "检查必要工具..."
    
    if ! command -v wrangler &> /dev/null; then
        print_error "错误: 未找到 wrangler CLI。请先安装: npm install -g wrangler"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "错误: 未找到 npm。请先安装 Node.js"
        exit 1
    fi
    
    print_success "✓ 所有必要工具已安装"
}

# 创建 KV 命名空间
setup_kv_namespace() {
    local env=$1
    print_info "为 $env 环境创建 KV 命名空间..."
    
    if [ "$env" = "dev" ]; then
        print_warning "开发环境已使用现有的 KV 命名空间"
        return
    fi
    
    if wrangler kv namespace create "GEMINI_CLI_KV" --env "$env"; then
        print_success "✓ $env 环境 KV 命名空间创建成功"
        print_warning "请将返回的 namespace ID 更新到 wrangler.toml 文件中"
    else
        print_error "✗ $env 环境 KV 命名空间创建失败"
    fi
}

# 设置环境密钥
setup_secrets() {
    local env=$1
    print_info "为 $env 环境设置密钥..."
    
    local env_file
    case $env in
        dev) env_file=".dev.vars" ;;
        staging) env_file=".staging.vars" ;;
        production) env_file=".production.vars" ;;
    esac
    
    if [ ! -f "$env_file" ]; then
        print_error "错误: 环境文件 $env_file 不存在"
        return
    fi
    
    print_warning "请手动设置以下密钥:"
    if [ "$env" = "dev" ]; then
        echo "wrangler secret put GCP_SERVICE_ACCOUNT"
        echo "wrangler secret put OPENAI_API_KEY"
    else
        echo "wrangler secret put GCP_SERVICE_ACCOUNT --env $env"
        echo "wrangler secret put OPENAI_API_KEY --env $env"
    fi
}

# 部署环境
deploy_environment() {
    local env=$1
    print_info "部署到 $env 环境..."
    
    local deploy_cmd
    case $env in
        dev) deploy_cmd="npm run deploy:dev" ;;
        staging) deploy_cmd="npm run deploy:staging" ;;
        production) deploy_cmd="npm run deploy:prod" ;;
    esac
    
    print_info "执行命令: $deploy_cmd"
    if eval "$deploy_cmd"; then
        print_success "✓ $env 环境部署成功"
    else
        print_error "✗ $env 环境部署失败"
    fi
}

# 测试环境
test_environment() {
    local env=$1
    print_info "测试 $env 环境..."
    
    local worker_name
    case $env in
        dev) worker_name="gemini-cli-worker-dev" ;;
        staging) worker_name="gemini-cli-worker-staging" ;;
        production) worker_name="gemini-cli-worker-prod" ;;
    esac
    
    # 获取 worker URL
    local url="https://$worker_name.your-subdomain.workers.dev"
    print_info "测试 URL: $url"
    
    # 测试健康检查
    if curl -s --max-time 10 "$url/health" > /dev/null; then
        print_success "✓ 健康检查通过"
        
        # 测试模型列表
        if curl -s --max-time 10 "$url/v1/models" > /dev/null; then
            print_success "✓ 模型列表获取成功"
        else
            print_error "✗ 模型列表获取失败"
        fi
    else
        print_error "✗ 环境测试失败"
    fi
}

# 查看环境状态
get_environment_status() {
    local env=$1
    print_info "查看 $env 环境状态..."
    
    if [ "$env" = "dev" ]; then
        wrangler deployments list
        wrangler secret list
    else
        wrangler deployments list --env "$env"
        wrangler secret list --env "$env"
    fi
}

# 处理单个环境
handle_single_environment() {
    local env=$1
    local action=$2
    
    case $action in
        setup)
            setup_kv_namespace "$env"
            setup_secrets "$env"
            ;;
        deploy)
            deploy_environment "$env"
            ;;
        test)
            test_environment "$env"
            ;;
        status)
            get_environment_status "$env"
            ;;
    esac
}

# 主逻辑
check_prerequisites

if [ "$ENVIRONMENT" = "all" ]; then
    for env in dev staging production; do
        print_info "处理 $env 环境..."
        handle_single_environment "$env" "$ACTION"
        echo ""
    done
else
    handle_single_environment "$ENVIRONMENT" "$ACTION"
fi

print_success "操作完成！"
