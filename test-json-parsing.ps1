# Test JSON parsing fix specifically
$headers = @{
    'Content-Type' = 'application/json'
    'Authorization' = 'Bearer sk-zxcvb1234567890qwertasdfg'
}

$baseUrl = 'https://gemini-cli-worker-testing.13467879663.workers.dev'

Write-Host "=== Testing JSON Parsing Fix ===" -ForegroundColor Green
Write-Host ""

# Test 1: Malformed JSON that should trigger our enhanced error handling
Write-Host "Test 1: Malformed JSON with bad escape (should return 400 with enhanced error)" -ForegroundColor Yellow
$malformedBody = '{"model": "gem\ini-2.5-flash", "messages": [{"role": "user", "content": "Hello"}]}'

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/v1/chat/completions" -Method POST -Headers $headers -Body $malformedBody -TimeoutSec 30
    Write-Host "❌ Unexpected success: $($response.StatusCode)" -ForegroundColor Red
    Write-Host "Response: $($response.Content)" -ForegroundColor Red
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "✅ Got expected error. Status: $statusCode" -ForegroundColor Green
    
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error Response: $errorBody" -ForegroundColor Cyan
        
        # Check if it's our enhanced error response
        if ($errorBody -and $errorBody.Trim() -ne "") {
            try {
                $errorJson = $errorBody | ConvertFrom-Json
                if ($errorJson.error -and $errorJson.error.code -eq "json_parse_error") {
                    Write-Host "🎉 Enhanced JSON error handling is working!" -ForegroundColor Green
                } elseif ($errorJson.error -and $errorJson.error.message -like "*JSON*") {
                    Write-Host "✅ JSON error detected in response" -ForegroundColor Green
                } else {
                    Write-Host "⚠️  Different error format: $($errorJson.error)" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "⚠️  Error response is not valid JSON" -ForegroundColor Yellow
            }
        } else {
            Write-Host "⚠️  Empty error response body" -ForegroundColor Yellow
        }
    }
}

Write-Host ""

# Test 2: Another type of JSON error
Write-Host "Test 2: Invalid JSON syntax (missing quote)" -ForegroundColor Yellow
$invalidJson = '{"model": "gemini-2.5-flash", "messages": [{"role": "user", "content": Hello}]}'

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/v1/chat/completions" -Method POST -Headers $headers -Body $invalidJson -TimeoutSec 30
    Write-Host "❌ Unexpected success: $($response.StatusCode)" -ForegroundColor Red
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "✅ Got expected error. Status: $statusCode" -ForegroundColor Green
    
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error Response: $errorBody" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "=== Summary ===" -ForegroundColor Green
Write-Host "If the tests above returned 400 errors (not 500), the JSON parsing fix is working." -ForegroundColor White
Write-Host "The enhanced error handling should catch JSON parsing errors before authentication." -ForegroundColor White
