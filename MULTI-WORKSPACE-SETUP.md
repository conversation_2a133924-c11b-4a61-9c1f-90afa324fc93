# 多工作区部署指南

本指南将帮助你在 Cloudflare Workers 上部署多个独立的工作区环境。

## 🚀 快速开始

### 1. 创建 KV 命名空间

为每个环境创建独立的 KV 命名空间：

```bash
# 创建测试环境 KV 命名空间
wrangler kv namespace create "GEMINI_CLI_KV" --env staging

# 创建生产环境 KV 命名空间  
wrangler kv namespace create "GEMINI_CLI_KV" --env production
```

记录返回的命名空间 ID，并更新 `wrangler.toml` 中对应的 `id` 字段。

### 2. 配置环境变量

#### 开发环境 (使用 .dev.vars)
- 已配置完成，使用当前的 `.dev.vars` 文件

#### 测试环境 (使用 .staging.vars)
编辑 `.staging.vars` 文件，填入测试环境的实际配置：
- 更新 `GCP_SERVICE_ACCOUNT` 为测试环境的 OAuth 凭据
- 设置 `GEMINI_PROJECT_ID` 为测试项目 ID
- 设置 `OPENAI_API_KEY` 为测试环境的 API 密钥

#### 生产环境 (使用 .production.vars)
编辑 `.production.vars` 文件，填入生产环境的实际配置：
- 更新 `GCP_SERVICE_ACCOUNT` 为生产环境的 OAuth 凭据
- 设置 `GEMINI_PROJECT_ID` 为生产项目 ID
- 设置 `OPENAI_API_KEY` 为生产环境的 API 密钥

### 3. 设置 Cloudflare Workers 密钥

为每个环境设置密钥（推荐用于生产环境）：

```bash
# 开发环境
wrangler secret put GCP_SERVICE_ACCOUNT
wrangler secret put OPENAI_API_KEY

# 测试环境
wrangler secret put GCP_SERVICE_ACCOUNT --env staging
wrangler secret put OPENAI_API_KEY --env staging

# 生产环境
wrangler secret put GCP_SERVICE_ACCOUNT --env production
wrangler secret put OPENAI_API_KEY --env production
```

### 4. 部署到不同环境

```bash
# 部署到开发环境
npm run deploy:dev

# 部署到测试环境
npm run deploy:staging

# 部署到生产环境
npm run deploy:prod
```

## 🔧 本地开发

同时运行多个环境进行本地测试：

```bash
# 终端 1 - 开发环境 (端口 8787)
npm run dev

# 终端 2 - 测试环境 (端口 8788)
npm run dev:staging

# 终端 3 - 生产环境 (端口 8789)
npm run dev:prod
```

## 🌐 访问地址

部署完成后，你将获得以下访问地址：

- **开发环境**: `https://gemini-cli-worker-dev.your-subdomain.workers.dev`
- **测试环境**: `https://gemini-cli-worker-staging.your-subdomain.workers.dev`
- **生产环境**: `https://gemini-cli-worker-prod.your-subdomain.workers.dev`

## 📋 环境配置差异

| 配置项 | 开发环境 | 测试环境 | 生产环境 |
|--------|----------|----------|----------|
| 假思考模式 | ✅ 启用 | ✅ 启用 | ❌ 关闭 |
| 真实思考模式 | ✅ 启用 | ✅ 启用 | ✅ 启用 |
| 思考内容流式传输 | ✅ 启用 | ✅ 启用 | ❌ 关闭 |
| 自动模型切换 | ✅ 启用 | ✅ 启用 | ✅ 启用 |
| 内容审核 | 宽松 | 中等 | 严格 |

## 🔒 安全建议

1. **生产环境必须启用 API 密钥认证**
2. **使用不同的 Google 账户/项目**
3. **定期轮换 API 密钥**
4. **监控各环境的使用情况**

## 🛠️ 故障排除

### 查看部署状态
```bash
wrangler deployments list --env staging
wrangler deployments list --env production
```

### 查看环境变量
```bash
wrangler secret list --env staging
wrangler secret list --env production
```

### 测试 API 端点
```bash
# 测试健康检查
curl https://gemini-cli-worker-staging.your-subdomain.workers.dev/health
curl https://gemini-cli-worker-prod.your-subdomain.workers.dev/health

# 测试模型列表
curl https://gemini-cli-worker-staging.your-subdomain.workers.dev/v1/models
```
