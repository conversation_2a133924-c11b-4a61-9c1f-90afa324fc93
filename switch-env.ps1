# PowerShell 脚本：快速环境切换
# 使用方法: .\switch-env.ps1 [environment]
# 示例: .\switch-env.ps1 staging

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "production")]
    [string]$Environment
)

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }
function Write-Info { Write-ColorOutput Cyan $args }

# 环境配置映射
$envConfig = @{
    "dev" = @{
        "name" = "gemini-cli-worker-dev"
        "port" = "8787"
        "vars_file" = ".dev.vars"
        "description" = "开发环境"
    }
    "staging" = @{
        "name" = "gemini-cli-worker-staging"
        "port" = "8788"
        "vars_file" = ".staging.vars"
        "description" = "测试环境"
    }
    "production" = @{
        "name" = "gemini-cli-worker-prod"
        "port" = "8789"
        "vars_file" = ".production.vars"
        "description" = "生产环境"
    }
}

$config = $envConfig[$Environment]

Write-Info "切换到 $($config.description) ($Environment)"
Write-Info "Worker 名称: $($config.name)"
Write-Info "本地端口: $($config.port)"
Write-Info "环境变量文件: $($config.vars_file)"

# 检查环境变量文件是否存在
if (!(Test-Path $config.vars_file)) {
    Write-Error "错误: 环境变量文件 $($config.vars_file) 不存在"
    Write-Warning "请先创建该文件或运行 .\deploy-multi-env.ps1 $Environment setup"
    exit 1
}

Write-Success "✓ 环境变量文件存在"

# 显示可用的操作
Write-Info "`n可用操作:"
Write-Output "1. 本地开发服务器:"
if ($Environment -eq "dev") {
    Write-Output "   npm run dev"
} else {
    Write-Output "   npm run dev:$Environment"
}

Write-Output "`n2. 部署到 Cloudflare:"
Write-Output "   npm run deploy:$Environment"

Write-Output "`n3. 构建检查:"
if ($Environment -eq "dev") {
    Write-Output "   npm run build"
} else {
    Write-Output "   npm run build:$Environment"
}

Write-Output "`n4. 查看部署状态:"
if ($Environment -eq "dev") {
    Write-Output "   wrangler deployments list"
} else {
    Write-Output "   wrangler deployments list --env $Environment"
}

Write-Output "`n5. 查看环境密钥:"
if ($Environment -eq "dev") {
    Write-Output "   wrangler secret list"
} else {
    Write-Output "   wrangler secret list --env $Environment"
}

# 询问是否启动本地开发服务器
$startDev = Read-Host "`n是否启动本地开发服务器? (y/N)"
if ($startDev -eq "y" -or $startDev -eq "Y") {
    Write-Info "启动 $($config.description) 本地开发服务器..."
    Write-Info "访问地址: http://localhost:$($config.port)"
    
    if ($Environment -eq "dev") {
        npm run dev
    } else {
        npm run "dev:$Environment"
    }
} else {
    Write-Info "环境切换完成。使用上述命令进行相应操作。"
}

# 显示环境信息摘要
Write-Info "`n环境信息摘要:"
Write-Output "环境: $($config.description)"
Write-Output "Worker: $($config.name)"
Write-Output "本地端口: $($config.port)"
Write-Output "配置文件: $($config.vars_file)"

if ($Environment -ne "dev") {
    Write-Output "部署URL: https://$($config.name).your-subdomain.workers.dev"
} else {
    Write-Output "部署URL: https://$($config.name).your-subdomain.workers.dev"
}

Write-Success "`n环境切换完成！"
