# Gemini CLI OpenAI Worker Environment Variables - PRE-PRODUCTION Environment

# Required: OAuth2 credentials <PERSON><PERSON><PERSON> from Gemini CLI authentication
# 预生产环境：最接近生产环境的测试环境
GCP_SERVICE_ACCOUNT={
  "access_token": "************************************************************************************************************************************************************************************************************************************",
  "refresh_token": "1//033Ra9n8Ktfo6CgYIARAAGAMSNwF-L9Ir-vzr-kdz7YoBjfvRPy8ttbRA9f01fcttkDHIxT0ei5ag6R0kwVCLq9nXJiKhWE0m24s",
  "scope": "https://www.googleapis.com/auth/cloud-platform https://www.googleapis.com/auth/userinfo.profile openid https://www.googleapis.com/auth/userinfo.email",
  "token_type": "Bearer",
  "id_token": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "expiry_date": 1753805563220
}

# Required: Google Cloud Project ID (预生产环境使用独立项目)
GEMINI_PROJECT_ID=august-apricot-391904

# Required: API key for authentication (预生产环境强制启用认证)
OPENAI_API_KEY=sk-pre-production-api-key-here

# Optional: Enable fake thinking output for thinking models
# 预生产环境关闭假思考模式，使用真实配置
ENABLE_FAKE_THINKING=false

# Optional: Enable real Gemini thinking output
# 预生产环境启用真实思考模式
ENABLE_REAL_THINKING=true

# Optional: Stream thinking as content with <thinking> tags
# 预生产环境使用与生产环境相同的配置
STREAM_THINKING_AS_CONTENT=false

# Optional: Auto switch from Pro to flash when rate-limited
ENABLE_AUTO_MODEL_SWITCHING=true

# Optional: Gemini Moderation Settings for pre-production
# 预生产环境使用与生产环境相同的严格审核设置
GEMINI_MODERATION_HARASSMENT_THRESHOLD=BLOCK_SOME
GEMINI_MODERATION_HATE_SPEECH_THRESHOLD=BLOCK_SOME
GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD=BLOCK_ONLY_HIGH
GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD=BLOCK_ONLY_HIGH
