# Quick test for the testing server
$headers = @{
    'Content-Type' = 'application/json'
    'Authorization' = 'Bearer sk-zxcvb1234567890qwertasdfg'
}

$body = @{
    model = "gemini-2.5-flash"
    messages = @(
        @{
            role = "user"
            content = "Hello"
        }
    )
} | ConvertTo-Json -Depth 3

Write-Host "Testing server: https://gemini-cli-worker-testing.13467879663.workers.dev"
Write-Host "Body: $body"

try {
    $response = Invoke-WebRequest -Uri 'https://gemini-cli-worker-testing.13467879663.workers.dev/v1/chat/completions' -Method POST -Headers $headers -Body $body -TimeoutSec 30
    Write-Host "Success: $($response.StatusCode)"
    Write-Host "Response: $($response.Content)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error Body: $errorBody"
    }
}
