# 从.pre-production.vars文件中读取GCP_SERVICE_ACCOUNT值
$varFile = Get-Content -Path ".pre-production.vars" -Raw
$patternServiceAccount = '(?s)GCP_SERVICE_ACCOUNT=(\{.*?\})(?:\r?\n)'
$patternProjectId = 'GEMINI_PROJECT_ID=([^\r\n]+)'

$matchesServiceAccount = [regex]::Match($varFile, $patternServiceAccount)
$matchesProjectId = [regex]::Match($varFile, $patternProjectId)

# 更新GCP_SERVICE_ACCOUNT
if ($matchesServiceAccount.Success -and $matchesServiceAccount.Groups.Count -gt 1) {
    $serviceAccount = $matchesServiceAccount.Groups[1].Value
    
    # 显示找到的值
    Write-Host "找到GCP_SERVICE_ACCOUNT值:" -ForegroundColor Green
    Write-Host $serviceAccount -ForegroundColor Cyan

    # 将值保存到临时文件
    $tempFile = [System.IO.Path]::GetTempFileName()
    $serviceAccount | Out-File -FilePath $tempFile -Encoding utf8

    # 使用wrangler更新密钥
    Write-Host "正在更新Wrangler GCP_SERVICE_ACCOUNT密钥..." -ForegroundColor Yellow
    Get-Content -Path $tempFile | wrangler secret put GCP_SERVICE_ACCOUNT --env pre-production

    # 清理临时文件
    Remove-Item -Path $tempFile
} else {
    Write-Host "在.pre-production.vars文件中未找到GCP_SERVICE_ACCOUNT值" -ForegroundColor Red
    exit 1
}

# 更新GEMINI_PROJECT_ID
if ($matchesProjectId.Success -and $matchesProjectId.Groups.Count -gt 1) {
    $projectId = $matchesProjectId.Groups[1].Value.Trim()
    
    # 显示找到的值
    Write-Host "找到GEMINI_PROJECT_ID值:" -ForegroundColor Green
    Write-Host $projectId -ForegroundColor Cyan

    # 将值保存到临时文件
    $tempFile = [System.IO.Path]::GetTempFileName()
    $projectId | Out-File -FilePath $tempFile -Encoding utf8

    # 使用wrangler更新密钥
    Write-Host "正在更新Wrangler GEMINI_PROJECT_ID密钥..." -ForegroundColor Yellow
    Get-Content -Path $tempFile | wrangler secret put GEMINI_PROJECT_ID --env pre-production

    # 清理临时文件
    Remove-Item -Path $tempFile
} else {
    Write-Host "在.pre-production.vars文件中未找到GEMINI_PROJECT_ID值" -ForegroundColor Red
    exit 1
} 